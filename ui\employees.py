from PyQt5.QtWidgets import (QW<PERSON>t, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

from ui.unified_styles import UnifiedStyles

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # إضافة العنوان الرئيسي
        title_label = QLabel("👷‍♂️ إدارة العمال المتطورة - نظام شامل لإدارة الموظفين والأجور")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                margin: 2px;
            }
            QTabBar::tab {
                min-width: 200px;
                max-width: 300px;
                height: 35px;
                padding: 8px 16px;
                font-size: 14px;
                border: 3px solid #000000;
                border-radius: 8px 8px 0 0;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: #ffffff;
                font-weight: 900;
            }
        """)

        # إنشاء التبويبات
        self.create_employees_tab()
        self.create_daily_wages_tab()

        main_layout.addWidget(self.tabs)
        self.setLayout(main_layout)

    def create_employees_tab(self):
        """إنشاء تبويب إدارة حسابات وبيانات العمال"""
        employees_widget = QWidget()
        layout = QVBoxLayout()

        # إطار البحث
        search_frame = QFrame()
        search_frame.setFixedHeight(55)
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 2px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 5, 10, 5)

        # تسمية البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #1e293b; font-weight: bold;")
        search_layout.addWidget(search_label)

        # حقل البحث
        self.employees_search_edit = QLineEdit()
        self.employees_search_edit.setPlaceholderText("البحث في بيانات العمال...")
        self.employees_search_edit.setFixedHeight(30)
        self.employees_search_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #000000;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 12px;
                background: #ffffff;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                background: #f0f9ff;
            }
        """)
        self.employees_search_edit.textChanged.connect(self.filter_employees)
        search_layout.addWidget(self.employees_search_edit)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # رسالة مؤقتة
        message_label = QLabel("📋 سيتم إضافة جدول العمال وإدارة البيانات قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: #f8fafc;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(message_label)

        employees_widget.setLayout(layout)
        self.tabs.addTab(employees_widget, "👥 إدارة حسابات وبيانات العمال")

    def create_daily_wages_tab(self):
        """إنشاء تبويب إدارة الأجور اليومية"""
        wages_widget = QWidget()
        layout = QVBoxLayout()

        # إطار البحث
        search_frame = QFrame()
        search_frame.setFixedHeight(55)
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 2px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 5, 10, 5)

        # تسمية البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #1e293b; font-weight: bold;")
        search_layout.addWidget(search_label)

        # حقل البحث
        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("البحث في الأجور اليومية...")
        self.daily_wages_search_edit.setFixedHeight(30)
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #000000;
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 12px;
                background: #ffffff;
                color: #1e293b;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                background: #f0f9ff;
            }
        """)
        self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
        search_layout.addWidget(self.daily_wages_search_edit)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # رسالة مؤقتة
        message_label = QLabel("💰 سيتم إضافة جدول الأجور اليومية وإدارة الرواتب قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: #f8fafc;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(message_label)

        wages_widget.setLayout(layout)
        self.tabs.addTab(wages_widget, "💰 إدارة الأجور اليومية")

    def filter_employees(self):
        """تصفية العمال - وظيفة مؤقتة"""
        print("🔍 البحث في العمال:", self.employees_search_edit.text())

    def filter_daily_wages(self):
        """تصفية الأجور اليومية - وظيفة مؤقتة"""
        print("🔍 البحث في الأجور اليومية:", self.daily_wages_search_edit.text())

    def save_daily_wages_data(self):
        """حفظ بيانات الأجور اليومية - وظيفة مؤقتة"""
        print("💾 حفظ بيانات الأجور اليومية")

    def refresh_data(self):
        """تحديث البيانات - وظيفة مؤقتة"""
        print("🔄 تحديث بيانات العمال")
