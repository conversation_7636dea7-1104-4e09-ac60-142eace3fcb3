from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget,
                            QDialog, QFormLayout, QTextEdit, QDateEdit, QDoubleSpinBox,
                            QComboBox, QMessageBox, QGroupBox, QSpacerItem, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPixmap

from database import Employee, EmployeePhone, Salary, DailyWage, get_session
from utils import format_currency
try:
    from ui.utils import show_error_message, show_info_message
except ImportError:
    def show_error_message(title, message):
        print(f"خطأ - {title}: {message}")
    def show_info_message(title, message):
        print(f"معلومات - {title}: {message}")
from ui.unified_styles import StyledTable, StyledTableFrame, StyledLabel, UnifiedStyles
import datetime

# حوارات مؤقتة بسيطة
class EmployeeDialog(QDialog):
    def __init__(self, parent=None, employee=None, session=None):
        super().__init__(parent)
        self.employee = employee
        self.session = session
        self.setWindowTitle("إضافة/تعديل عامل")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout()
        layout.addWidget(QLabel("حوار إضافة/تعديل العامل - قيد التطوير"))

        buttons_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        cancel_button = QPushButton("إلغاء")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_data(self):
        return {}

class DailyWageDialog(QDialog):
    def __init__(self, parent=None, daily_wage=None, session=None):
        super().__init__(parent)
        self.daily_wage = daily_wage
        self.session = session
        self.setWindowTitle("إضافة/تعديل أجر يومي")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout()
        layout.addWidget(QLabel("حوار إضافة/تعديل الأجر اليومي - قيد التطوير"))

        buttons_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        cancel_button = QPushButton("إلغاء")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_data(self):
        return {}

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال مع تبويبين: إدارة حسابات وبيانات العمال، وإدارة الأجور اليومية"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للمشاريع مع تقليل المساحات الفارغة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إنشاء تبويبات لإدارة حسابات وبيانات العمال وإدارة الأجور اليومية مع تنسيق مطابق للمشاريع
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.3 rgba(241, 245, 249, 0.9),
                    stop:0.7 rgba(226, 232, 240, 0.85),
                    stop:1 rgba(255, 255, 255, 0.9));
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #475569, stop:0.3 #64748b,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #64748b,
                    stop:0.8 #475569, stop:1 #334155);
                border: 3px solid #1e40af;
                border-bottom: 3px solid #1e40af;
                color: #ffffff;
                font-size: 20px;
                font-weight: bold;
                padding: 8px 20px;
                margin-right: 2px;
                min-width: 876px;
                max-width: 876px;
                height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3),
                           0 1px 4px rgba(0, 0, 0, 0.15);
                border-radius: 10px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e40af, stop:0.2 #1d4ed8, stop:0.3 #2563eb,
                    stop:0.4 #3b82f6, stop:0.6 #60a5fa, stop:0.7 #2563eb,
                    stop:0.8 #1d4ed8, stop:1 #1e40af);
                border: 4px solid #60a5fa;
                border-bottom: 4px solid #60a5fa;
                color: #ffffff;
                min-width: 878px;
                max-width: 878px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(96, 165, 250, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                min-width: 880px;
                max-width: 880px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # تبويب إدارة حسابات وبيانات العمال مع تقليل المساحات الفارغة
        employees_tab = QWidget()
        employees_layout = QVBoxLayout()
        employees_layout.setContentsMargins(1, 1, 1, 1)  # تقليل الهوامش من 5 إلى 1
        employees_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للمشاريع
        title_label = QLabel("👷‍♂️ إدارة العمال المتطورة - نظام شامل ومتقدم لإدارة العمال مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        employees_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للمشاريع
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch()

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch()

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # حقل البحث المطور والمحسن للعمال - مختلف عن العملاء والموردين
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("👷‍♂️ البحث في سجل العمال - الاسم، المنصب، الهاتف، البريد أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_employees)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # زر البحث المطور مع تأثيرات بصرية مطابق للفواتير
        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_employees)
        search_button.setToolTip("بحث متقدم في العمال")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء الجدول المتطور للعمال
        self.create_advanced_employees_table()

        # إنشاء إطار الجدول مع العلامة المائية
        table_frame = StyledTableFrame()
        table_layout = QVBoxLayout()
        table_layout.setContentsMargins(1, 1, 1, 1)
        table_layout.setSpacing(2)

        # إضافة العلامة المائية للجدول
        self.add_watermark_to_employees_table()

        table_layout.addWidget(self.employees_table)
        table_frame.frame.setLayout(table_layout)

        # إنشاء إطار الأزرار السفلي مطابق للمشاريع
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(0, 0, 0, 0)
        bottom_container.setSpacing(0)

        # تخطيط أفقي للأزرار
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(8, 8, 8, 8)
        actions_layout.setSpacing(8)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للمشاريع

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عامل")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_employee)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'blue')  # أزرق كلاسيكي
        self.edit_button.clicked.connect(self.edit_employee)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'red')  # أحمر للحذف
        self.delete_button.clicked.connect(self.delete_employee)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.salary_button = QPushButton("💰 إدارة الرواتب ▼")
        self.style_advanced_button(self.salary_button, 'purple', has_menu=True)  # بنفسجي للرواتب
        self.salary_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.reports_button = QPushButton("📊 التقارير ▼")
        self.style_advanced_button(self.reports_button, 'orange', has_menu=True)  # برتقالي للتقارير
        self.reports_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط الأفقي
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.salary_button)
        actions_layout.addWidget(self.reports_button)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع تخطيط تبويب العمال
        employees_layout.addWidget(top_frame)
        employees_layout.addWidget(table_frame.frame, 1)  # إعطاء الجدول أولوية في التمدد
        employees_layout.addWidget(bottom_frame)
        employees_tab.setLayout(employees_layout)

        # تبويب إدارة الأجور اليومية
        daily_wages_tab = QWidget()
        daily_wages_layout = QVBoxLayout()
        daily_wages_layout.setContentsMargins(1, 1, 1, 1)
        daily_wages_layout.setSpacing(2)

        # إضافة العنوان الرئيسي للأجور اليومية
        daily_wages_title = QLabel("💰 إدارة الأجور اليومية - نظام متطور لتسجيل ومتابعة الأجور اليومية للعمال")
        daily_wages_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        daily_wages_title.setAlignment(Qt.AlignCenter)
        daily_wages_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        daily_wages_layout.addWidget(daily_wages_title)

        # إطار البحث للأجور اليومية
        daily_wages_search_frame = QFrame()
        daily_wages_search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط البحث للأجور اليومية
        daily_wages_search_layout = QHBoxLayout()
        daily_wages_search_layout.setContentsMargins(8, 8, 8, 8)
        daily_wages_search_layout.setSpacing(8)

        # حاوي عمودي للتوسيط
        daily_wages_search_container = QVBoxLayout()
        daily_wages_search_container.setContentsMargins(0, 0, 0, 0)
        daily_wages_search_container.setSpacing(0)
        daily_wages_search_container.addStretch()
        daily_wages_search_container.addLayout(daily_wages_search_layout)
        daily_wages_search_container.addStretch()

        # عناصر البحث للأجور اليومية
        daily_wages_search_label = QLabel("🔍 بحث:")
        daily_wages_search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        daily_wages_search_label.setAlignment(Qt.AlignCenter)

        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("💰 البحث في الأجور اليومية - اسم العامل، التاريخ، المبلغ...")
        self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # إضافة عناصر البحث للأجور اليومية
        daily_wages_search_layout.addWidget(daily_wages_search_label, 0, Qt.AlignVCenter)
        daily_wages_search_layout.addWidget(self.daily_wages_search_edit, 2, Qt.AlignVCenter)

        daily_wages_search_frame.setLayout(daily_wages_search_container)

        # إنشاء جدول الأجور اليومية
        self.create_advanced_daily_wages_table()

        # إنشاء إطار جدول الأجور اليومية
        daily_wages_table_frame = StyledTableFrame()
        daily_wages_table_layout = QVBoxLayout()
        daily_wages_table_layout.setContentsMargins(1, 1, 1, 1)
        daily_wages_table_layout.setSpacing(2)

        # إضافة العلامة المائية لجدول الأجور اليومية
        self.add_watermark_to_daily_wages_table()

        daily_wages_table_layout.addWidget(self.daily_wages_table)
        daily_wages_table_frame.frame.setLayout(daily_wages_table_layout)

        # إطار أزرار الأجور اليومية
        daily_wages_buttons_frame = QFrame()
        daily_wages_buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # حاوي عمودي للتوسيط
        daily_wages_buttons_container = QVBoxLayout()
        daily_wages_buttons_container.setContentsMargins(0, 0, 0, 0)
        daily_wages_buttons_container.setSpacing(0)

        # تخطيط أفقي للأزرار
        daily_wages_buttons_layout = QHBoxLayout()
        daily_wages_buttons_layout.setContentsMargins(8, 8, 8, 8)
        daily_wages_buttons_layout.setSpacing(8)

        daily_wages_buttons_container.addStretch(1)
        daily_wages_buttons_container.addLayout(daily_wages_buttons_layout)
        daily_wages_buttons_container.addStretch(1)

        # أزرار الأجور اليومية
        self.add_daily_wage_button = QPushButton("➕ إضافة أجر يومي")
        self.style_advanced_button(self.add_daily_wage_button, 'emerald')
        self.add_daily_wage_button.clicked.connect(self.add_daily_wage)
        self.add_daily_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_daily_wage_button = QPushButton("✏️ تعديل أجر")
        self.style_advanced_button(self.edit_daily_wage_button, 'blue')
        self.edit_daily_wage_button.clicked.connect(self.edit_daily_wage)
        self.edit_daily_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_daily_wage_button = QPushButton("🗑️ حذف أجر")
        self.style_advanced_button(self.delete_daily_wage_button, 'red')
        self.delete_daily_wage_button.clicked.connect(self.delete_daily_wage)
        self.delete_daily_wage_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_daily_wages_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_daily_wages_button, 'modern_teal')
        self.refresh_daily_wages_button.clicked.connect(self.refresh_daily_wages_data)
        self.refresh_daily_wages_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.daily_wages_reports_button = QPushButton("📊 تقارير الأجور ▼")
        self.style_advanced_button(self.daily_wages_reports_button, 'orange', has_menu=True)
        self.daily_wages_reports_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إضافة الأزرار للتخطيط
        daily_wages_buttons_layout.addWidget(self.add_daily_wage_button)
        daily_wages_buttons_layout.addWidget(self.edit_daily_wage_button)
        daily_wages_buttons_layout.addWidget(self.delete_daily_wage_button)
        daily_wages_buttons_layout.addWidget(self.refresh_daily_wages_button)
        daily_wages_buttons_layout.addWidget(self.daily_wages_reports_button)

        daily_wages_buttons_frame.setLayout(daily_wages_buttons_container)

        # تجميع تخطيط تبويب الأجور اليومية
        daily_wages_layout.addWidget(daily_wages_search_frame)
        daily_wages_layout.addWidget(daily_wages_table_frame.frame, 1)
        daily_wages_layout.addWidget(daily_wages_buttons_frame)
        daily_wages_tab.setLayout(daily_wages_layout)

        # إضافة التبويبات مع أيقونات مثل المشاريع
        self.tabs.addTab(employees_tab, "👷‍♂️ إدارة حسابات وبيانات العمال")
        self.tabs.addTab(daily_wages_tab, "💰 إدارة الأجور اليومية")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث للعمال
            self.search_edit.textChanged.connect(self.filter_employees)
            # ربط حدث البحث للأجور اليومية
            self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
            print("✅ تم ربط أحداث العمال بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث العمال: {str(e)}")

    def create_advanced_employees_table(self):
        """إنشاء جدول العمال المتطور والمحسن مطابق للمشاريع"""
        styled_table = StyledTable()
        self.employees_table = styled_table.table
        self.employees_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للمشاريع
        headers = [
            "🆔 ID",
            "👷‍♂️ اسم العامل",
            "💼 المنصب",
            "📍 العنوان",
            "📧 البريد الإلكتروني",
            "📞 رقم الهاتف",
            "💰 الرصيد",
            "📊 الحالة",
            "📅 تاريخ التوظيف"
        ]
        self.employees_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول المتطورة مطابقة للمشاريع
        self.employees_table.setAlternatingRowColors(True)
        self.employees_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.employees_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.employees_table.setSortingEnabled(True)
        self.employees_table.setShowGrid(True)

        # إعدادات الرؤوس والأعمدة
        self.employees_table.verticalHeader().setVisible(False)
        self.employees_table.horizontalHeader().setStretchLastSection(False)

        # تحديد عرض الأعمدة مطابق للمشاريع
        header = self.employees_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تحديد عرض محدد للأعمدة
        header.resizeSection(0, 110)   # ID - 110px
        header.resizeSection(1, 300)   # اسم العامل - 300px
        header.resizeSection(2, 300)   # المنصب - 300px
        header.resizeSection(3, 300)   # العنوان - 300px
        header.resizeSection(4, 300)   # البريد - 300px
        header.resizeSection(5, 170)   # الهاتف - 170px
        header.resizeSection(6, 170)   # الرصيد - 170px
        header.resizeSection(7, 170)   # الحالة - 170px
        header.resizeSection(8, 170)   # تاريخ التوظيف - 170px

        # إضافة العلامة المائية
        self.add_watermark_to_employees_table()

        # تحديث البيانات
        self.refresh_data()

    def create_advanced_daily_wages_table(self):
        """إنشاء جدول الأجور اليومية المتطور والمحسن"""
        styled_table = StyledTable()
        self.daily_wages_table = styled_table.table
        self.daily_wages_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة
        headers = [
            "🆔 ID",
            "👷‍♂️ اسم العامل",
            "📅 التاريخ",
            "💰 المبلغ",
            "⏰ ساعات العمل",
            "📋 ملاحظات"
        ]
        self.daily_wages_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول المتطورة
        self.daily_wages_table.setAlternatingRowColors(True)
        self.daily_wages_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.daily_wages_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.daily_wages_table.setSortingEnabled(True)
        self.daily_wages_table.setShowGrid(True)

        # إعدادات الرؤوس والأعمدة
        self.daily_wages_table.verticalHeader().setVisible(False)
        self.daily_wages_table.horizontalHeader().setStretchLastSection(False)

        # تحديد عرض الأعمدة
        header = self.daily_wages_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        header.resizeSection(0, 110)   # ID - 110px
        header.resizeSection(1, 300)   # اسم العامل - 300px
        header.resizeSection(2, 170)   # التاريخ - 170px
        header.resizeSection(3, 170)   # المبلغ - 170px
        header.resizeSection(4, 170)   # ساعات العمل - 170px
        header.resizeSection(5, 300)   # ملاحظات - 300px

        # إضافة العلامة المائية
        self.add_watermark_to_daily_wages_table()

        # تحديث البيانات
        self.refresh_daily_wages_data()

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow)
        filter_layout.addWidget(self.current_filter_label, 1)  # يأخذ المساحة المتبقية
        filter_layout.addWidget(self.filter_menu_button)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.left_arrow.clicked.connect(self.show_filter_menu)
        self.filter_menu_button.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(filter_layout)

    def filter_employees(self):
        """تصفية العمال بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Employee.name.like(f"%{search_text}%") |
                    Employee.phone.like(f"%{search_text}%") |
                    Employee.email.like(f"%{search_text}%") |
                    Employee.position.like(f"%{search_text}%") |
                    Employee.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Employee.balance > 0)
            elif status == "normal":
                query = query.filter(Employee.balance == 0)
            elif status == "debtor":
                query = query.filter(Employee.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            employees = query.order_by(Employee.id.asc()).all()

            # تحديث الجدول
            self.populate_employees_table(employees)

        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")

    def filter_daily_wages(self):
        """تصفية الأجور اليومية بناءً على نص البحث"""
        try:
            search_text = self.daily_wages_search_edit.text().strip().lower()

            # بناء الاستعلام
            query = self.session.query(DailyWage).join(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Employee.name.like(f"%{search_text}%") |
                    DailyWage.notes.like(f"%{search_text}%")
                )

            # تنفيذ الاستعلام (من الأحدث للأقدم)
            daily_wages = query.order_by(DailyWage.date.desc()).all()

            # تحديث الجدول
            self.populate_daily_wages_table(daily_wages)

        except Exception as e:
            print(f"خطأ في تصفية الأجور اليومية: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات جدول العمال"""
        try:
            # جلب جميع العمال من قاعدة البيانات (من الأقدم للأحدث)
            employees = self.session.query(Employee).order_by(Employee.id.asc()).all()
            self.populate_employees_table(employees)
        except Exception as e:
            print(f"خطأ في تحميل بيانات العمال: {str(e)}")

    def refresh_daily_wages_data(self):
        """تحديث بيانات جدول الأجور اليومية"""
        try:
            # جلب جميع الأجور اليومية من قاعدة البيانات (من الأحدث للأقدم)
            daily_wages = self.session.query(DailyWage).join(Employee).order_by(DailyWage.date.desc()).all()
            self.populate_daily_wages_table(daily_wages)
        except Exception as e:
            print(f"خطأ في تحميل بيانات الأجور اليومية: {str(e)}")

    def populate_employees_table(self, employees):
        """ملء جدول العمال بالبيانات"""
        try:
            self.employees_table.setRowCount(len(employees))

            for row, employee in enumerate(employees):
                # ID مع أيقونة ديناميكية حسب الحالة المالية
                balance = employee.balance or 0
                if balance > 0:
                    id_icon = "🟢"  # أخضر للنشط
                elif balance == 0:
                    id_icon = "🟡"  # أصفر للعادي
                else:
                    id_icon = "🔴"  # أحمر للمدين

                id_item = QTableWidgetItem(f"{id_icon} {employee.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                self.employees_table.setItem(row, 0, id_item)

                # اسم العامل
                name_text = employee.name if employee.name and employee.name.strip() else "No data"
                name_item = QTableWidgetItem(f"👷‍♂️ {name_text}")
                name_item.setTextAlignment(Qt.AlignCenter)
                if name_text == "No data":
                    name_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 1, name_item)

                # المنصب
                position_text = employee.position if employee.position and employee.position.strip() else "No data"
                position_item = QTableWidgetItem(f"💼 {position_text}")
                position_item.setTextAlignment(Qt.AlignCenter)
                if position_text == "No data":
                    position_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 2, position_item)

                # العنوان
                address_text = employee.address if employee.address and employee.address.strip() else "No data"
                address_item = QTableWidgetItem(f"📍 {address_text}")
                address_item.setTextAlignment(Qt.AlignCenter)
                if address_text == "No data":
                    address_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 3, address_item)

                # البريد الإلكتروني
                email_text = employee.email if employee.email and employee.email.strip() else "No data"
                email_item = QTableWidgetItem(f"📧 {email_text}")
                email_item.setTextAlignment(Qt.AlignCenter)
                if email_text == "No data":
                    email_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 4, email_item)

                # رقم الهاتف
                phone_text = employee.phone if employee.phone and employee.phone.strip() else "No data"
                phone_item = QTableWidgetItem(f"📞 {phone_text}")
                phone_item.setTextAlignment(Qt.AlignCenter)
                if phone_text == "No data":
                    phone_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 5, phone_item)

                # الرصيد مع ألوان
                balance_item = QTableWidgetItem(f"💰 {format_currency(balance)}")
                balance_item.setTextAlignment(Qt.AlignCenter)
                if balance > 0:
                    balance_item.setForeground(QColor("#10b981"))  # أخضر للموجب
                elif balance < 0:
                    balance_item.setForeground(QColor("#ef4444"))  # أحمر للسالب
                self.employees_table.setItem(row, 6, balance_item)

                # الحالة
                status_text = self.get_employee_status(balance)
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)
                self.employees_table.setItem(row, 7, status_item)

                # تاريخ التوظيف
                hire_date = employee.hire_date.strftime("%Y-%m-%d") if employee.hire_date else "No data"
                date_item = QTableWidgetItem(f"📅 {hire_date}")
                date_item.setTextAlignment(Qt.AlignCenter)
                if hire_date == "No data":
                    date_item.setForeground(QColor("#ef4444"))
                self.employees_table.setItem(row, 8, date_item)

        except Exception as e:
            print(f"خطأ في ملء جدول العمال: {str(e)}")

    def populate_daily_wages_table(self, daily_wages):
        """ملء جدول الأجور اليومية بالبيانات"""
        try:
            self.daily_wages_table.setRowCount(len(daily_wages))

            for row, wage in enumerate(daily_wages):
                # ID
                id_item = QTableWidgetItem(f"🆔 {wage.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                self.daily_wages_table.setItem(row, 0, id_item)

                # اسم العامل
                employee_name = wage.employee.name if wage.employee else "No data"
                name_item = QTableWidgetItem(f"👷‍♂️ {employee_name}")
                name_item.setTextAlignment(Qt.AlignCenter)
                if employee_name == "No data":
                    name_item.setForeground(QColor("#ef4444"))
                self.daily_wages_table.setItem(row, 1, name_item)

                # التاريخ
                date_text = wage.date.strftime("%Y-%m-%d") if wage.date else "No data"
                date_item = QTableWidgetItem(f"📅 {date_text}")
                date_item.setTextAlignment(Qt.AlignCenter)
                if date_text == "No data":
                    date_item.setForeground(QColor("#ef4444"))
                self.daily_wages_table.setItem(row, 2, date_item)

                # المبلغ مع لون أخضر
                amount = wage.amount or 0
                amount_item = QTableWidgetItem(f"💰 {format_currency(amount)}")
                amount_item.setTextAlignment(Qt.AlignCenter)
                amount_item.setForeground(QColor("#10b981"))  # أخضر للمبلغ
                self.daily_wages_table.setItem(row, 3, amount_item)

                # ساعات العمل
                hours = wage.hours_worked or 0
                hours_item = QTableWidgetItem(f"⏰ {hours}")
                hours_item.setTextAlignment(Qt.AlignCenter)
                self.daily_wages_table.setItem(row, 4, hours_item)

                # ملاحظات
                notes_text = wage.notes if wage.notes and wage.notes.strip() else "No data"
                notes_item = QTableWidgetItem(f"📋 {notes_text}")
                notes_item.setTextAlignment(Qt.AlignCenter)
                if notes_text == "No data":
                    notes_item.setForeground(QColor("#ef4444"))
                self.daily_wages_table.setItem(row, 5, notes_item)

        except Exception as e:
            print(f"خطأ في ملء جدول الأجور اليومية: {str(e)}")

    def get_employee_status(self, balance):
        """تحديد حالة العامل بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    # وظائف العمال
    def add_employee(self):
        """إضافة عامل جديد"""
        try:
            dialog = EmployeeDialog(self, session=self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # إنشاء عامل جديد
                    employee = Employee(**data)
                    self.session.add(employee)
                    self.session.commit()
                    show_info_message("تم", "تم إضافة العامل بنجاح")
                    self.refresh_data()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة العامل: {str(e)}")
            self.session.rollback()

    def edit_employee(self):
        """تعديل عامل"""
        try:
            selected_row = self.employees_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عامل من القائمة")
                return

            # استخراج ID من النص (إزالة الأيقونة)
            id_text = self.employees_table.item(selected_row, 0).text()
            employee_id = int(id_text.split()[-1])  # أخذ آخر جزء بعد المسافة

            employee = self.session.query(Employee).get(employee_id)
            if not employee:
                show_error_message("خطأ", "لم يتم العثور على العامل")
                return

            dialog = EmployeeDialog(self, employee, self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # تحديث بيانات العامل
                    for key, value in data.items():
                        setattr(employee, key, value)

                    self.session.commit()
                    show_info_message("تم", "تم تحديث العامل بنجاح")
                    self.refresh_data()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تعديل العامل: {str(e)}")
            self.session.rollback()

    def delete_employee(self):
        """حذف عامل"""
        try:
            selected_row = self.employees_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار عامل من القائمة")
                return

            # استخراج ID من النص
            id_text = self.employees_table.item(selected_row, 0).text()
            employee_id = int(id_text.split()[-1])

            employee = self.session.query(Employee).get(employee_id)
            if not employee:
                show_error_message("خطأ", "لم يتم العثور على العامل")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(self, "تأكيد الحذف",
                                       f"هل أنت متأكد من حذف العامل '{employee.name}'؟\n"
                                       "سيتم حذف جميع البيانات المرتبطة به.",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                self.session.delete(employee)
                self.session.commit()
                show_info_message("تم", "تم حذف العامل بنجاح")
                self.refresh_data()
                self.refresh_daily_wages_data()  # تحديث جدول الأجور أيضاً
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حذف العامل: {str(e)}")
            self.session.rollback()

    # وظائف الأجور اليومية
    def add_daily_wage(self):
        """إضافة أجر يومي جديد"""
        try:
            dialog = DailyWageDialog(self, session=self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # إنشاء أجر يومي جديد
                    daily_wage = DailyWage(**data)
                    self.session.add(daily_wage)
                    self.session.commit()
                    show_info_message("تم", "تم إضافة الأجر اليومي بنجاح")
                    self.refresh_daily_wages_data()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إضافة الأجر اليومي: {str(e)}")
            self.session.rollback()

    def edit_daily_wage(self):
        """تعديل أجر يومي"""
        try:
            selected_row = self.daily_wages_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار أجر يومي من القائمة")
                return

            # استخراج ID من النص
            id_text = self.daily_wages_table.item(selected_row, 0).text()
            wage_id = int(id_text.split()[-1])

            daily_wage = self.session.query(DailyWage).get(wage_id)
            if not daily_wage:
                show_error_message("خطأ", "لم يتم العثور على الأجر اليومي")
                return

            dialog = DailyWageDialog(self, daily_wage, self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # تحديث بيانات الأجر اليومي
                    for key, value in data.items():
                        setattr(daily_wage, key, value)

                    self.session.commit()
                    show_info_message("تم", "تم تحديث الأجر اليومي بنجاح")
                    self.refresh_daily_wages_data()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تعديل الأجر اليومي: {str(e)}")
            self.session.rollback()

    def delete_daily_wage(self):
        """حذف أجر يومي"""
        try:
            selected_row = self.daily_wages_table.currentRow()
            if selected_row < 0:
                show_error_message("خطأ", "الرجاء اختيار أجر يومي من القائمة")
                return

            # استخراج ID من النص
            id_text = self.daily_wages_table.item(selected_row, 0).text()
            wage_id = int(id_text.split()[-1])

            daily_wage = self.session.query(DailyWage).get(wage_id)
            if not daily_wage:
                show_error_message("خطأ", "لم يتم العثور على الأجر اليومي")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(self, "تأكيد الحذف",
                                       f"هل أنت متأكد من حذف هذا الأجر اليومي؟",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                self.session.delete(daily_wage)
                self.session.commit()
                show_info_message("تم", "تم حذف الأجر اليومي بنجاح")
                self.refresh_daily_wages_data()
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حذف الأجر اليومي: {str(e)}")
            self.session.rollback()

    def add_watermark_to_employees_table(self):
        """إضافة علامة مائية لجدول العمال مطابقة للمشاريع"""
        try:
            # إنشاء العلامة المائية مثل المشاريع
            watermark = QLabel("Smart Finish", self.employees_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # تحديد موقع وحجم العلامة المائية لتغطي كامل الجدول
            def update_watermark_geometry():
                if self.employees_table.isVisible():
                    watermark.setGeometry(self.employees_table.rect())

            # ربط تحديث الموقع بتغيير حجم الجدول
            self.employees_table.resizeEvent = lambda event: (
                QTableWidget.resizeEvent(self.employees_table, event),
                update_watermark_geometry()
            )[-1]

            # تحديث أولي للموقع
            update_watermark_geometry()

            # جعل العلامة المائية في الخلف
            watermark.lower()
            watermark.show()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية لجدول العمال: {str(e)}")

    def add_watermark_to_daily_wages_table(self):
        """إضافة علامة مائية لجدول الأجور اليومية مطابقة للمشاريع"""
        try:
            # إنشاء العلامة المائية مثل المشاريع
            watermark = QLabel("Smart Finish", self.daily_wages_table)
            watermark.setAlignment(Qt.AlignCenter)

            # تنسيق العلامة المائية مكبرة أكثر للتوحيد مع الباقي
            font = QFont("Arial", 150, QFont.Bold)
            watermark.setFont(font)

            # تطبيق تصميم محسن للعلامة المائية - أكثر وضوحاً لكن لا يؤثر على البيانات
            watermark.setStyleSheet("""
                QLabel {
                    color: rgba(71, 85, 105, 0.35);
                    background: transparent;
                    border: none;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
                    font-weight: bold;
                }
            """)

            # تحديد موقع وحجم العلامة المائية لتغطي كامل الجدول
            def update_watermark_geometry():
                if self.daily_wages_table.isVisible():
                    watermark.setGeometry(self.daily_wages_table.rect())

            # ربط تحديث الموقع بتغيير حجم الجدول
            self.daily_wages_table.resizeEvent = lambda event: (
                QTableWidget.resizeEvent(self.daily_wages_table, event),
                update_watermark_geometry()
            )[-1]

            # تحديث أولي للموقع
            update_watermark_geometry()

            # جعل العلامة المائية في الخلف
            watermark.lower()
            watermark.show()

        except Exception as e:
            print(f"خطأ في إضافة العلامة المائية لجدول الأجور اليومية: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للمشاريع"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للمشاريع
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#065f46', 'bg_end': '#047857', 'bg_bottom': '#059669',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#065f46', 'pressed_bottom': '#047857', 'pressed_border': '#059669',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'blue': {
                    'bg_start': '#1e3a8a', 'bg_mid': '#1e40af', 'bg_end': '#1d4ed8', 'bg_bottom': '#2563eb',
                    'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#93c5fd', 'hover_bottom': '#dbeafe',
                    'hover_border': '#3b82f6', 'pressed_start': '#1e3a8a', 'pressed_mid': '#1e40af',
                    'pressed_end': '#1d4ed8', 'pressed_bottom': '#2563eb', 'pressed_border': '#1d4ed8',
                    'border': '#3b82f6', 'text': '#ffffff', 'shadow': 'rgba(59, 130, 246, 0.5)'
                },
                'red': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#fca5a5', 'hover_bottom': '#fecaca',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#dc2626',
                    'border': '#ef4444', 'text': '#ffffff', 'shadow': 'rgba(239, 68, 68, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#134e4a', 'bg_mid': '#115e59', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#14b8a6', 'hover_mid': '#5eead4', 'hover_end': '#99f6e4', 'hover_bottom': '#ccfbf1',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#115e59', 'pressed_bottom': '#0f766e', 'pressed_border': '#0d9488',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#312e81', 'bg_mid': '#3730a3', 'bg_end': '#4338ca', 'bg_bottom': '#4f46e5',
                    'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#a5b4fc', 'hover_bottom': '#c7d2fe',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#4f46e5',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#6b21a8', 'bg_end': '#7c2d12', 'bg_bottom': '#8b5cf6',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#a855f7', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c2d12', 'pressed_border': '#8b5cf6',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.5)'
                },
                'orange': {
                    'bg_start': '#9a3412', 'bg_mid': '#c2410c', 'bg_end': '#ea580c', 'bg_bottom': '#f97316',
                    'hover_start': '#fb923c', 'hover_mid': '#fdba74', 'hover_end': '#fed7aa', 'hover_bottom': '#ffedd5',
                    'hover_border': '#fb923c', 'pressed_start': '#7c2d12', 'pressed_mid': '#9a3412',
                    'pressed_end': '#c2410c', 'pressed_bottom': '#ea580c', 'pressed_border': '#f97316',
                    'border': '#fb923c', 'text': '#ffffff', 'shadow': 'rgba(251, 146, 60, 0.5)'
                }
            }

            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق النمط المتطور مع تأثيرات بصرية مذهلة
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    min-height: 35px;
                    max-height: 45px;
                    box-shadow: 0 4px 8px {color_scheme['shadow']},
                               0 2px 4px rgba(0, 0, 0, 0.1);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 3px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 6px 12px {color_scheme['shadow']},
                               0 4px 8px rgba(0, 0, 0, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 2px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 2px 4px {color_scheme['shadow']},
                               0 1px 2px rgba(0, 0, 0, 0.2);
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق نمط الزر: {str(e)}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للمشاريع"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
            }
        """)

        # إضافة العناصر للتخطيط
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(filter_layout)

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة مطابقة للمشاريع"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("عمال نشطين", "active"),
            ("عمال عاديين", "normal"),
            ("عمال مدينين", "debtor")
        ]

        for text, value in filter_options:
            centered_text = f"{text:^35}"
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def show_filter_menu(self):
        """عرض قائمة التصفية مطابق للمشاريع"""
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية مطابق للمشاريع"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_employees()
