from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView, QTabWidget,
                            QDialog, QFormLayout, QTextEdit, QDateEdit, QDoubleSpinBox,
                            QComboBox, QMessageBox, QGroupBox, QSpacerItem, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QPixmap

from database import Employee, EmployeePhone, Salary, DailyWage, get_session
from utils import format_currency
from ui.unified_styles import StyledTable, StyledTableFrame, StyledLabel, UnifiedStyles
import datetime

# حوارات مؤقتة بسيطة
class EmployeeDialog(QDialog):
    def __init__(self, parent=None, employee=None, session=None):
        super().__init__(parent)
        self.employee = employee
        self.session = session
        self.setWindowTitle("إضافة/تعديل عامل")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        layout.addWidget(QLabel("حوار إضافة/تعديل العامل - قيد التطوير"))
        
        buttons_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        cancel_button = QPushButton("إلغاء")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def get_data(self):
        return {}

class DailyWageDialog(QDialog):
    def __init__(self, parent=None, daily_wage=None, session=None):
        super().__init__(parent)
        self.daily_wage = daily_wage
        self.session = session
        self.setWindowTitle("إضافة/تعديل أجر يومي")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        layout.addWidget(QLabel("حوار إضافة/تعديل الأجر اليومي - قيد التطوير"))
        
        buttons_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        cancel_button = QPushButton("إلغاء")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def get_data(self):
        return {}

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال مع تبويبين: إدارة حسابات وبيانات العمال، وإدارة الأجور اليومية"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للمشاريع مع تقليل المساحات الفارغة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إنشاء تبويبات لإدارة حسابات وبيانات العمال وإدارة الأجور اليومية مع تنسيق مطابق للمشاريع
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.3 rgba(241, 245, 249, 0.9),
                    stop:0.7 rgba(226, 232, 240, 0.85),
                    stop:1 rgba(255, 255, 255, 0.9));
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #475569, stop:0.3 #64748b,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #64748b,
                    stop:0.8 #475569, stop:1 #334155);
                border: 3px solid #1e40af;
                border-bottom: 3px solid #1e40af;
                color: #ffffff;
                font-size: 20px;
                font-weight: bold;
                padding: 8px 20px;
                margin-right: 2px;
                min-width: 876px;
                max-width: 876px;
                height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3),
                           0 1px 4px rgba(0, 0, 0, 0.15);
                border-radius: 10px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e40af, stop:0.2 #1d4ed8, stop:0.3 #2563eb,
                    stop:0.4 #3b82f6, stop:0.6 #60a5fa, stop:0.7 #2563eb,
                    stop:0.8 #1d4ed8, stop:1 #1e40af);
                border: 4px solid #60a5fa;
                border-bottom: 4px solid #60a5fa;
                color: #ffffff;
                min-width: 878px;
                max-width: 878px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(96, 165, 250, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                min-width: 880px;
                max-width: 880px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)
        
        # تبويب إدارة حسابات وبيانات العمال مع تقليل المساحات الفارغة
        employees_tab = QWidget()
        employees_layout = QVBoxLayout()
        employees_layout.setContentsMargins(1, 1, 1, 1)  # تقليل الهوامش من 5 إلى 1
        employees_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للمشاريع
        title_label = QLabel("👷‍♂️ إدارة العمال المتطورة - نظام شامل ومتقدم لإدارة العمال مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        employees_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للمشاريع
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # تخطيط أفقي للبحث
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # عناصر البحث
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("👷‍♂️ البحث في العمال - الاسم، المنصب، الهاتف، البريد...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # إضافة عناصر البحث
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إضافة رسالة بسيطة بدلاً من الجدول والأزرار
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 20px;
            }
        """)

        content_layout = QVBoxLayout()
        content_layout.setAlignment(Qt.AlignCenter)

        message_label = QLabel("👷‍♂️ تبويب إدارة حسابات وبيانات العمال")
        message_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 40px;
            }
        """)

        sub_message = QLabel("تم إنشاء التبويب بنجاح مع التصميم الموحد\nسيتم إضافة الجدول والوظائف لاحقاً")
        sub_message.setFont(QFont("Segoe UI", 16))
        sub_message.setAlignment(Qt.AlignCenter)
        sub_message.setStyleSheet("""
            QLabel {
                color: #6b7280;
                background: transparent;
                border: none;
                padding: 20px;
            }
        """)

        content_layout.addWidget(message_label)
        content_layout.addWidget(sub_message)
        content_frame.setLayout(content_layout)

        # تجميع تخطيط تبويب العمال
        employees_layout.addWidget(top_frame)
        employees_layout.addWidget(content_frame, 1)  # إعطاء المحتوى أولوية في التمدد
        employees_tab.setLayout(employees_layout)

        # تبويب إدارة الأجور اليومية
        daily_wages_tab = QWidget()
        daily_wages_layout = QVBoxLayout()
        daily_wages_layout.setContentsMargins(1, 1, 1, 1)
        daily_wages_layout.setSpacing(2)

        # إضافة العنوان الرئيسي للأجور اليومية
        daily_wages_title = QLabel("💰 إدارة الأجور اليومية - نظام متطور لتسجيل ومتابعة الأجور اليومية للعمال")
        daily_wages_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        daily_wages_title.setAlignment(Qt.AlignCenter)
        daily_wages_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        daily_wages_layout.addWidget(daily_wages_title)

        # إطار البحث للأجور اليومية
        daily_wages_search_frame = QFrame()
        daily_wages_search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط البحث للأجور اليومية
        daily_wages_search_layout = QHBoxLayout()
        daily_wages_search_layout.setContentsMargins(8, 8, 8, 8)
        daily_wages_search_layout.setSpacing(8)

        # حاوي عمودي للتوسيط
        daily_wages_search_container = QVBoxLayout()
        daily_wages_search_container.setContentsMargins(0, 0, 0, 0)
        daily_wages_search_container.setSpacing(0)
        daily_wages_search_container.addStretch()
        daily_wages_search_container.addLayout(daily_wages_search_layout)
        daily_wages_search_container.addStretch()

        # عناصر البحث للأجور اليومية
        daily_wages_search_label = QLabel("🔍 بحث:")
        daily_wages_search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        daily_wages_search_label.setAlignment(Qt.AlignCenter)

        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("💰 البحث في الأجور اليومية - اسم العامل، التاريخ، المبلغ...")
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # إضافة عناصر البحث للأجور اليومية
        daily_wages_search_layout.addWidget(daily_wages_search_label, 0, Qt.AlignVCenter)
        daily_wages_search_layout.addWidget(self.daily_wages_search_edit, 2, Qt.AlignVCenter)

        daily_wages_search_frame.setLayout(daily_wages_search_container)

        # إضافة رسالة بسيطة بدلاً من الجدول والأزرار
        daily_wages_content_frame = QFrame()
        daily_wages_content_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 20px;
            }
        """)

        daily_wages_content_layout = QVBoxLayout()
        daily_wages_content_layout.setAlignment(Qt.AlignCenter)

        daily_wages_message_label = QLabel("💰 تبويب إدارة الأجور اليومية للعمال")
        daily_wages_message_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        daily_wages_message_label.setAlignment(Qt.AlignCenter)
        daily_wages_message_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 40px;
            }
        """)

        daily_wages_sub_message = QLabel("تم إنشاء التبويب بنجاح مع التصميم الموحد\nسيتم إضافة الجدول والوظائف لاحقاً")
        daily_wages_sub_message.setFont(QFont("Segoe UI", 16))
        daily_wages_sub_message.setAlignment(Qt.AlignCenter)
        daily_wages_sub_message.setStyleSheet("""
            QLabel {
                color: #6b7280;
                background: transparent;
                border: none;
                padding: 20px;
            }
        """)

        daily_wages_content_layout.addWidget(daily_wages_message_label)
        daily_wages_content_layout.addWidget(daily_wages_sub_message)
        daily_wages_content_frame.setLayout(daily_wages_content_layout)

        # تجميع تخطيط تبويب الأجور اليومية
        daily_wages_layout.addWidget(daily_wages_search_frame)
        daily_wages_layout.addWidget(daily_wages_content_frame, 1)
        daily_wages_tab.setLayout(daily_wages_layout)

        # إضافة التبويبات مع أيقونات مثل المشاريع
        self.tabs.addTab(employees_tab, "👷‍♂️ إدارة حسابات وبيانات العمال")
        self.tabs.addTab(daily_wages_tab, "💰 إدارة الأجور اليومية")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث للعمال
            self.search_edit.textChanged.connect(self.filter_employees)
            # ربط حدث البحث للأجور اليومية
            self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)
            print("✅ تم ربط أحداث العمال بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث العمال: {str(e)}")

    def filter_employees(self):
        """تصفية العمال - وظيفة مؤقتة"""
        print("🔍 البحث في العمال:", self.search_edit.text())

    def filter_daily_wages(self):
        """تصفية الأجور اليومية - وظيفة مؤقتة"""
        print("🔍 البحث في الأجور اليومية:", self.daily_wages_search_edit.text())
