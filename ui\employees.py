from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

from ui.unified_styles import UnifiedStyles

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # إنشاء التبويبات مع تصميم مطابق للإشعارات
        self.tabs = QTabWidget()
        # تعيين موضع التبويبات في الأعلى (الافتراضي)
        self.tabs.setTabPosition(QTabWidget.North)

        # إضافة عنوان ديناميكي يتغير حسب التبويبة المحددة
        self.title_label = QLabel("👷‍♂️ إدارة بيانات وحسابات العمال - نظام شامل ومتقدم لإدارة الموظفين والمعلومات الشخصية")
        self.title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        # تحسين تصميم التبويبات مع إطار أسود وعرض أكبر مطابق للإشعارات
        self.tabs.setStyleSheet("""
            QTabWidget {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #f8fafc;
                font-size: 14px;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background-color: #ffffff;
                top: -3px;
            }
            QTabBar {
                alignment: center;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 30px;
                padding: 8px 32px;
                margin: 2px;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                font-weight: bold;
                font-size: 20px;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                border-radius: 12px;
                margin-top: -1px;
                padding: 9px 32px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                border: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 878px;
                max-width: 878px;
                min-height: 30px;
                padding: 8px 32px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3),
                           0 2px 8px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إنشاء التبويبات
        self.create_employees_tab()
        self.create_daily_wages_tab()

        # ربط تغيير التبويبة بتحديث العنوان
        self.tabs.currentChanged.connect(self.update_title)

        # إضافة العناصر للتخطيط بالترتيب الصحيح
        main_layout.addWidget(self.tabs)  # التبويبات أولاً
        main_layout.addWidget(self.title_label)  # العنوان تحت التبويبات مباشرة
        self.setLayout(main_layout)

    def create_employees_tab(self):
        """إنشاء تبويب إدارة حسابات وبيانات العمال مطابق للفواتير"""
        employees_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # إنشاء إطار علوي محسن مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن مطابق للفواتير
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(4)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مطابقة للفواتير
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-weight: bold;
                padding: 5px 8px;
                background: transparent;
                border: none;
                min-width: 60px;
                max-width: 80px;
            }
        """)

        # حقل البحث محسن مطابق للفواتير
        self.employees_search_edit = QLineEdit()
        self.employees_search_edit.setPlaceholderText("البحث في بيانات العمال...")
        self.employees_search_edit.setFixedHeight(35)
        self.employees_search_edit.setStyleSheet("""
            QLineEdit {
                border: 3px solid #64748b;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background: #ffffff;
                color: #1e293b;
                min-width: 200px;
                selection-background-color: #3b82f6;
                selection-color: #ffffff;
            }
            QLineEdit:focus {
                border: 3px solid #3b82f6;
                background: #f0f9ff;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
            QLineEdit:hover {
                border: 3px solid #475569;
                background: #f8fafc;
            }
        """)
        self.employees_search_edit.textChanged.connect(self.filter_employees)

        # زر البحث مطابق للفواتير
        search_button = QPushButton("🔍 بحث")
        search_button.setFixedHeight(35)
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: #ffffff;
                border: 3px solid #1e40af;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.5 #1d4ed8, stop:1 #1e40af);
                border: 3px solid #1e3a8a;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
                transform: translateY(1px);
            }
        """)

        # إضافة جميع العناصر للصف الواحد
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.employees_search_edit, 2, Qt.AlignVCenter)
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي
        top_frame.setLayout(top_container)

        # إضافة الإطار العلوي للتخطيط
        layout.addWidget(top_frame)

        # رسالة مؤقتة محسنة
        message_label = QLabel("📋 سيتم إضافة جدول العمال وإدارة البيانات قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 20px;
                margin: 2px;
                min-height: 60px;
            }
        """)
        layout.addWidget(message_label, 1)

        employees_widget.setLayout(layout)
        self.tabs.addTab(employees_widget, "👥 إدارة بيانات وحسابات العمال")

    def create_daily_wages_tab(self):
        """إنشاء تبويب إدارة الأجور اليومية مطابق للفواتير"""
        wages_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # إنشاء إطار علوي محسن مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن مطابق للفواتير
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(4)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مطابقة للفواتير
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                font-weight: bold;
                padding: 5px 8px;
                background: transparent;
                border: none;
                min-width: 60px;
                max-width: 80px;
            }
        """)

        # حقل البحث محسن مطابق للفواتير
        self.daily_wages_search_edit = QLineEdit()
        self.daily_wages_search_edit.setPlaceholderText("البحث في الأجور اليومية...")
        self.daily_wages_search_edit.setFixedHeight(35)
        self.daily_wages_search_edit.setStyleSheet("""
            QLineEdit {
                border: 3px solid #64748b;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background: #ffffff;
                color: #1e293b;
                min-width: 200px;
                selection-background-color: #3b82f6;
                selection-color: #ffffff;
            }
            QLineEdit:focus {
                border: 3px solid #3b82f6;
                background: #f0f9ff;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
            QLineEdit:hover {
                border: 3px solid #475569;
                background: #f8fafc;
            }
        """)
        self.daily_wages_search_edit.textChanged.connect(self.filter_daily_wages)

        # زر البحث مطابق للفواتير
        search_button = QPushButton("🔍 بحث")
        search_button.setFixedHeight(35)
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: #ffffff;
                border: 3px solid #1e40af;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.5 #1d4ed8, stop:1 #1e40af);
                border: 3px solid #1e3a8a;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
                transform: translateY(1px);
            }
        """)

        # إضافة جميع العناصر للصف الواحد
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.daily_wages_search_edit, 2, Qt.AlignVCenter)
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي
        top_frame.setLayout(top_container)

        # إضافة الإطار العلوي للتخطيط
        layout.addWidget(top_frame)

        # رسالة مؤقتة محسنة
        message_label = QLabel("💰 سيتم إضافة جدول الأجور اليومية وإدارة الرواتب قريباً")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        message_label.setStyleSheet("""
            QLabel {
                color: #1e293b;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 20px;
                margin: 2px;
                min-height: 60px;
            }
        """)
        layout.addWidget(message_label, 1)

        wages_widget.setLayout(layout)
        self.tabs.addTab(wages_widget, "💰 إدارة الأجور اليومية للعمال")

    def filter_employees(self):
        """تصفية العمال - وظيفة مؤقتة"""
        print("🔍 البحث في العمال:", self.employees_search_edit.text())

    def filter_daily_wages(self):
        """تصفية الأجور اليومية - وظيفة مؤقتة"""
        print("🔍 البحث في الأجور اليومية:", self.daily_wages_search_edit.text())

    def save_daily_wages_data(self):
        """حفظ بيانات الأجور اليومية - وظيفة مؤقتة"""
        print("💾 حفظ بيانات الأجور اليومية")

    def update_title(self, index):
        """تحديث العنوان حسب التبويبة المحددة"""
        try:
            if index == 0:  # تبويبة إدارة البيانات
                self.title_label.setText("👥 إدارة بيانات وحسابات العمال - نظام شامل ومتقدم لإدارة الموظفين والمعلومات الشخصية والوظيفية")
            elif index == 1:  # تبويبة الأجور
                self.title_label.setText("💰 إدارة الأجور اليومية للعمال - نظام متكامل ومتطور للمحاسبة والتقارير المالية والرواتب")
        except Exception as e:
            pass  # خطأ في تحديث العنوان

    def refresh_data(self):
        """تحديث البيانات - وظيفة مؤقتة"""
        print("🔄 تحديث بيانات العمال")
