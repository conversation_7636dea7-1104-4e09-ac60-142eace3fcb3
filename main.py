import sys
import gc
import traceback
import os
import warnings
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType
from PyQt5.QtGui import QFont

# إعداد إخفاء التحذيرات
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

class SilentFilter:
    """مرشح صامت للرسائل التقنية"""
    def __init__(self):
        self.blocked_patterns = [
            'Unknown property', 'text-shadow', 'box-shadow', 'transform',
            'transition', 'does not have a property named', 'alignment',
            'overflow', 'text-overflow', 'backdrop-filter', 'letter-spacing'
        ]

    def write(self, message):
        if not any(pattern in str(message) for pattern in self.blocked_patterns):
            sys.__stderr__.write(message)

    def flush(self):
        sys.__stderr__.flush()

# تطبيق المرشح الصامت
sys.stderr = SilentFilter()

# تعطيل رسائل Qt
def qt_message_handler(mode, context, message):
    blocked_qt = ['Unknown property', 'text-shadow', 'box-shadow', 'transform', 'alignment']
    if not any(pattern in message for pattern in blocked_qt):
        if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
            print(f"Qt: {message}")

qInstallMessageHandler(qt_message_handler)

from ui.main_window import MainWindow
from database import init_db, get_session, User
from performance_optimizer import apply_performance_optimizations, setup_performance_monitoring
from utils import check_due_invoices, check_due_supplier_payments, check_upcoming_payments

def setup_application():
    """إعداد تطبيق PyQt"""
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("برنامج المحاسبة الإداري")

    # تعيين نمط التطبيق
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass  # تجاهل أخطاء الخط

    return app



def main():
    """الدالة الرئيسية للتطبيق"""
    try:
        # تطبيق تحسينات الأداء قبل بدء البرنامج
        apply_performance_optimizations()

        # إعداد تطبيق PyQt
        app = setup_application()

        # تعطيل تحديث الشاشة المستمر لتحسين الأداء
        app.setEffectEnabled(Qt.UI_AnimateCombo, False)
        app.setEffectEnabled(Qt.UI_AnimateTooltip, False)
        app.setEffectEnabled(Qt.UI_FadeMenu, False)
        app.setEffectEnabled(Qt.UI_FadeTooltip, False)
        app.setEffectEnabled(Qt.UI_AnimateMenu, False)

        # إعداد قاعدة البيانات
        init_db()

        # إنشاء جلسة قاعدة البيانات
        session = get_session()

        # إنشاء أو الحصول على المستخدم الإداري الافتراضي
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            # إنشاء مستخدم إداري افتراضي
            user = User(
                username="admin",
                password="admin",
                role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()

        # إنشاء النافذة الرئيسية مباشرة
        window = MainWindow(session, user)

        # إعداد مراقبة الأداء للنافذة الرئيسية
        setup_performance_monitoring(window)

        # تنظيف الذاكرة قبل عرض النافذة
        gc.collect()

        # إظهار النافذة الرئيسية
        window.showMaximized()

        print("✅ تم تشغيل البرنامج بنجاح")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"حدث خطأ غير متوقع: {str(e)}\n\n{traceback.format_exc()}"

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print(error_message)

        sys.exit(1)

if __name__ == "__main__":
    main()
